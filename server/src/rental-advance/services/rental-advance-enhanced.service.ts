import {
  Injectable,
  BadRequestException,
  NotFoundException,
  InternalServerErrorException,
  Logger,
} from '@nestjs/common';
import { PrismaService } from '../../prisma.service';
import { RentalAdvanceService } from '../rental-advance.service';
import { RentalAdvanceMappingService } from './rental-advance-mapping.service';
import { User } from '@prisma/client';
import { RentalAdvanceStatus } from '../enums/rental-status.enum';
import {
  ExtractedDataResponseDto,
  ProposalResponseDto,
  FinalConfirmationResponseDto
} from '../dto/rental-advance-response.dto';
import { ConfirmExtractedDataDto } from '../dto/confirm-extracted-data.dto';
import { FinalConfirmationDto } from '../dto/final-confirmation.dto';

@Injectable()
export class RentalAdvanceServiceEnhanced {
  private readonly logger = new Logger(RentalAdvanceServiceEnhanced.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly baseService: RentalAdvanceService,
    private readonly mappingService: RentalAdvanceMappingService,
  ) { }

  /**
   * Lista solicitações do usuário com paginação
   */
  async listUserRequestsPaginated(
    userId: string,
    page: number = 1,
    limit: number = 10,
    status?: string,
  ) {
    try {
      const skip = (page - 1) * limit;
      const where = {
        userId,
        ...(status && { currentStatus: status }),
      };

      const [data, total] = await Promise.all([
        this.prisma.rentalAdvanceRequest.findMany({
          where,
          include: {
            realEstate: { select: { name: true, cnpj: true } },
            statusLogs: {
              orderBy: { createdAt: 'desc' },
              take: 1,
            },
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit,
        }),
        this.prisma.rentalAdvanceRequest.count({ where }),
      ]);

      return { data, total };
    } catch (error) {
      this.logger.error(`Erro ao listar solicitações paginadas: ${error.message}`);
      throw new InternalServerErrorException('Erro ao recuperar solicitações');
    }
  }

  /**
   * Busca dados extraídos formatados
   */
  async getExtractedDataFormatted(user: User, operationId: string): Promise<ExtractedDataResponseDto> {
    try {
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: { id: operationId, userId: user.id },
        include: { contractData: true },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      const extractionStatus = this._getExtractionStatus(operation.currentStatus as RentalAdvanceStatus);
      const extractedData = operation.contractData ? this._mapContractData(operation) : undefined;

      return this.mappingService.toExtractedDataResponse(
        operationId,
        extractionStatus,
        extractedData,
        [], // TODO: implementar validações
      );
    } catch (error) {
      this.logger.error(`Erro ao buscar dados extraídos para operação ${operationId}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar dados extraídos');
    }
  }

  /**
   * Mapeia o status da operação para o status da extração.
   * @private
   */
  private _getExtractionStatus(status: RentalAdvanceStatus): 'processing' | 'completed' | 'failed' | 'pending_review' {
    const statusMap = {
      [RentalAdvanceStatus.PDF_EXTRACTED]: 'completed',
      [RentalAdvanceStatus.EXTRACTION_FAILED]: 'failed',
    };
    return statusMap[status] || 'processing';
  }

  /**
   * Mapeia os dados do contrato da operação para o formato de resposta.
   * @private
   */
  private _mapContractData(operation: any) {
    const { contractData, rentAmount } = operation;
    return {
      propertyAddress: contractData.propertyAddress,
      landlordName: contractData.landlordName,
      tenantName: contractData.tenantName,
      landlordDocument: contractData.landlordDocument,
      tenantDocument: contractData.tenantDocument,
      rentalGuarantee: contractData.rentalGuarantee,
      contractTerm: contractData.contractTerm,
      startDate: contractData.startDate?.toISOString(),
      endDate: contractData.endDate?.toISOString(),
      propertyRegistry: contractData.propertyRegistry,
      rentAmount: Number(rentAmount),
    };
  }

  /**
   * Busca proposta formatada
   */
  async getProposal(user: User, operationId: string): Promise<ProposalResponseDto> {
    try {
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: operationId,
          userId: user.id,
        },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      if (operation.currentStatus === RentalAdvanceStatus.REJECTED) {
        return {
          operationId,
          proposal: {
            liquidAmount: -1, // Valor líquido que o cliente recebe
            totalAmount: -1, // Valor total da operação
            monthlyAmount: -1, // Valor mensal descontado
            proposedMonths: -1,
            interestRate: -1,
            fees: {
              origination: -1,
              service: -1,
              total: -1,
            },
            schedule: [
              {
                month: -1,
                discountAmount: -1,
                remainingBalance: -1
              }
            ]
          },
          expiresAt: "",
          termsAndConditions: "",
          nextSteps: []
        }
      }

      if (operation.currentStatus !== RentalAdvanceStatus.PROPOSAL_SENT) {
        throw new BadRequestException('Proposta ainda não foi gerada');
      }

      if (!operation.proposalAmount || !operation.monthlyRentOffer || !operation.proposedMonths) {
        throw new BadRequestException('Proposta incompleta');
      }

      const liquidAmount = Number(operation.proposalAmount);
      const monthlyAmount = Number(operation.monthlyRentOffer);
      const proposedMonths = operation.proposedMonths;
      const totalAmount = monthlyAmount * proposedMonths;

      // Calcular taxas (simuladas para exemplo)
      const originationFee = liquidAmount * 0.02; // 2%
      const serviceFee = liquidAmount * 0.01; // 1%
      const totalFees = originationFee + serviceFee;

      // Gerar cronograma
      const schedule = Array.from({ length: proposedMonths }, (_, index) => ({
        month: index + 1,
        discountAmount: monthlyAmount,
        remainingBalance: totalAmount - (monthlyAmount * (index + 1)),
      }));

      return {
        operationId,
        proposal: {
          liquidAmount,
          totalAmount,
          monthlyAmount,
          proposedMonths,
          interestRate: 5.5, // Simulado
          fees: {
            origination: originationFee,
            service: serviceFee,
            total: totalFees,
          },
          schedule,
        },
        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 dias
        termsAndConditions: 'Termos e condições aplicáveis conforme contrato.',
        nextSteps: [
          'Revise os valores da proposta',
          'Aceite os termos e condições',
          'Prossiga para envio dos documentos',
        ],
      };
    } catch (error) {
      this.logger.error(`Erro ao buscar proposta: ${error.message}`);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao buscar proposta');
    }
  }

  /**
   * Finaliza confirmação com resposta formatada
   */
  async finalConfirmationFormatted(
    user: User,
    dto: FinalConfirmationDto,
    identityDoc: Express.Multer.File,
  ): Promise<FinalConfirmationResponseDto> {
    try {
      // Usar o método base para processamento
      const result = await this.baseService.finalConfirmation(user, dto, identityDoc);

      return {
        operationId: result.operationId,
        status: result.status,
        message: 'Solicitação finalizada com sucesso! Nossa equipe analisará em breve.',
        estimatedDepositDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000).toISOString(), // 3 dias úteis
        trackingId: `LPC-${result.operationId.slice(-8).toUpperCase()}`,
        nextSteps: [
          'Aguarde análise da nossa equipe',
          'Você será notificado por WhatsApp sobre o status',
          'Em caso de aprovação, o valor será depositado em até 2 dias úteis',
        ],
        supportContact: {
          whatsapp: '+55 11 99999-9999',
          email: '<EMAIL>',
          hours: 'Segunda a Sexta, 9h às 18h',
        },
      };
    } catch (error) {
      this.logger.error(`Erro na confirmação final formatada: ${error.message}`);
      throw error;
    }
  }

  /**
   * Cancela operação
   */
  async cancelOperation(user: User, operationId: string, reason?: string) {
    try {
      const operation = await this.prisma.rentalAdvanceRequest.findFirst({
        where: {
          id: operationId,
          userId: user.id,
        },
      });

      if (!operation) {
        throw new NotFoundException('Operação não encontrada');
      }

      // Verificar se pode cancelar
      const cancellableStatuses = [
        RentalAdvanceStatus.CREATED,
        RentalAdvanceStatus.PDF_UPLOADED,
        RentalAdvanceStatus.PDF_EXTRACTED,
        RentalAdvanceStatus.DATA_CONFIRMED,
        RentalAdvanceStatus.PENDING_PROPOSAL,
        RentalAdvanceStatus.PROPOSAL_SENT,
      ];

      if (!cancellableStatuses.includes(operation.currentStatus as RentalAdvanceStatus)) {
        throw new BadRequestException('Não é possível cancelar operação neste status');
      }

      // Atualizar status
      await this.prisma.rentalAdvanceRequest.update({
        where: { id: operationId },
        data: { currentStatus: RentalAdvanceStatus.CANCELLED },
      });

      // Adicionar log
      await this.addStatusLog(operationId, RentalAdvanceStatus.CANCELLED, reason);

      return {
        operationId,
        status: RentalAdvanceStatus.CANCELLED,
      };
    } catch (error) {
      this.logger.error(`Erro ao cancelar operação: ${error.message}`);
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new InternalServerErrorException('Erro ao cancelar operação');
    }
  }

  // Métodos de webhook removidos - agora usamos resposta direta do N8N



  private async addStatusLog(
    operationId: string,
    status: string,
    notes?: string,
  ): Promise<void> {
    try {
      await this.prisma.rentalRequestStatusLog.create({
        data: {
          rentalRequestId: operationId,
          status,
        },
      });
    } catch (error) {
      this.logger.error(`Erro ao adicionar log de status: ${error.message}`);
    }
  }
}
