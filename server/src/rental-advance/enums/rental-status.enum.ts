export enum RentalAdvanceStatus {
  CREATED = 'created',
  PDF_UPLOADED = 'pdf_uploaded',
  PROCESSING_EXTRACTION = 'processing_extraction',
  PDF_EXTRACTED = 'pdf_extracted',
  EXTRACTION_FAILED = 'extraction_failed',
  DATA_CONFIRMED = 'data_confirmed',
  PENDING_PROPOSAL = 'pending_proposal',
  PROCESSING_PROPOSAL = 'processing_proposal',
  PROPOSAL_SENT = 'proposal_sent',
  DOCS_UPLOADED = 'docs_uploaded',
  AWAITING_REVIEW = 'awaiting_review',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  CANCELLED = 'cancelled',
}

export enum PixKeyType {
  CPF = 'cpf',
  EMAIL = 'email',
  PHONE = 'phone',
  RANDOM = 'random',
}
