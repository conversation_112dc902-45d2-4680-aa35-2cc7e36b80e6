import { Module } from '@nestjs/common';
import { N8nService } from './n8n.service';
import { N8nCallbackController } from './n8n-callback.controller';
import { N8nAuthGuard } from './guards/n8n-auth.guard';
import { PrismaService } from '../../prisma.service';
import { DriveService } from '../drive/drive.service';

@Module({
  controllers: [N8nCallbackController],
  providers: [N8nService, N8nAuthGuard, PrismaService, DriveService],
  exports: [N8nService],
})
export class N8nModule {}
