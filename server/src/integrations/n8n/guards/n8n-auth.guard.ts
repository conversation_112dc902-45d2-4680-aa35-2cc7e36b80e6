import {
  Injectable,
  CanActivate,
  ExecutionContext,
  UnauthorizedException,
  Logger,
} from '@nestjs/common';

@Injectable()
export class N8nAuthGuard implements CanActivate {
  private readonly logger = new Logger(N8nAuthGuard.name);

  canActivate(context: ExecutionContext): boolean {
    const request = context.switchToHttp().getRequest();
    const authHeader = request.headers['x-backend-auth'];
    const expectedAuth = process.env.N8N_AUTH_HEADER;

    if (!expectedAuth) {
      this.logger.error('N8N_AUTH_HEADER environment variable not configured');
      throw new UnauthorizedException('N8N authentication not configured');
    }

    if (!authHeader) {
      this.logger.warn('N8N callback request missing authentication header');
      throw new UnauthorizedException('Missing authentication header');
    }

    if (authHeader !== expectedAuth) {
      this.logger.warn('N8N callback request with invalid authentication header');
      throw new UnauthorizedException('Invalid authentication header');
    }

    return true;
  }
}
