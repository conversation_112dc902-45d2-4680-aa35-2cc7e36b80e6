import { IsString, IsNotEmpty, IsOptional, IsObject, IsBoolean } from 'class-validator';

export class BaseN8nCallbackDto {
  @IsString()
  @IsNotEmpty()
  operationId: string;

  @IsString()
  @IsNotEmpty()
  timestamp: string;

  @IsBoolean()
  success: boolean;

  @IsString()
  @IsOptional()
  error?: string;
}

export class ContractExtractionCallbackDto extends BaseN8nCallbackDto {
  @IsObject()
  @IsOptional()
  extractedData?: {
    propertyAddress?: string;
    landlordName?: string;
    tenantName?: string;
    landlordDocument?: string;
    tenantDocument?: string;
    rentalGuarantee?: string;
    contractTerm?: string;
    startDate?: string;
    endDate?: string;
    propertyRegistry?: string;
    [key: string]: any; // Allow additional fields
  };
}

export class ProposalCallbackDto extends BaseN8nCallbackDto {
  @IsObject()
  @IsOptional()
  proposalData?: {
    proposalAmount?: number;
    monthlyRentOffer?: number;
    proposedMonths?: number;
    [key: string]: any; // Allow additional fields
  };
}
