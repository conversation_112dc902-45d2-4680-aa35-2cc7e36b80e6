import { Controller, Post, Body, HttpCode, HttpStatus, HttpException } from '@nestjs/common';
import { AuthService } from './auth.service';
import { LoginDto } from './dto/login.dto';
import { VerifyCodeDto } from './dto/verify-code.dto';
import { Public } from '../common/decorators/public.decorator';
import { AuthRateLimit } from '../rate-limit/rate-limit.decorator';
import { JwtService, TokenExpiredError } from '@nestjs/jwt';
import { PrismaService } from 'src/prisma.service';
import { ApiResponse, ApiResponseBuilder } from 'src/common/dto/api-response.dto';
import { AuthResponseDto, LoginResponseDto, ResendCodeResponseDto } from './dto/auth-response.dto';

@Controller('auth')
@Public()
@AuthRateLimit()
export class AuthController {

  constructor(
    private readonly authService: AuthService,
    private readonly jwtService: JwtService,
    private readonly prisma: PrismaService,
  ) { }

  /**
   * Login ou criação de usuário com envio de código
   */
  @Post('login')
  @HttpCode(HttpStatus.OK)
  async login(@Body() dto: LoginDto): Promise<ApiResponse<LoginResponseDto>> {
    try {
      const result = await this.authService.loginOrCreateUser(dto);

      const responseData: LoginResponseDto = {
        message: 'Código de verificação enviado para seu WhatsApp.',
        codeSent: result.codeSent,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutos
        userId: result.user.id, // Para logs internos
      };

      return ApiResponseBuilder.success(
        responseData,
        'Login processado com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          error.message || 'Erro interno do servidor',
          'LOGIN_ERROR',
          error.details || null
        ),
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('verify')
  @HttpCode(HttpStatus.OK)
  async verify(@Body() dto: VerifyCodeDto): Promise<ApiResponse<AuthResponseDto>> {
    try {
      const result = await this.authService.verifyCode(dto);

      const responseData: AuthResponseDto = {
        user: {
          id: result.user.id,
          name: result.user.name,
          phone: result.user.phone || undefined,
          createdAt: result.user.createdAt.toISOString(),
        },
        accessToken: result.accessToken,
        tokenType: 'Bearer',
        expiresIn: 7 * 24 * 60 * 60, // 7 dias em segundos
      };

      return ApiResponseBuilder.success(
        responseData,
        'Autenticação realizada com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          error.message || 'Código inválido ou expirado',
          'VERIFY_CODE_ERROR',
          error.details || null
        ),
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  /**
   * Reenvio de código de verificação
   */
  @Post('resend-code')
  @HttpCode(HttpStatus.OK)
  async resendCode(@Body() body: { cpf: string }): Promise<ApiResponse<ResendCodeResponseDto>> {
    try {
      const result = await this.authService.resendCode(body.cpf);

      const responseData: ResendCodeResponseDto = {
        message: 'Novo código de verificação enviado.',
        codeSent: result.codeSent,
        expiresAt: new Date(Date.now() + 5 * 60 * 1000).toISOString(), // 5 minutos
        canResendAgainAt: new Date(Date.now() + 60000).toISOString(), // 1 minuto
      };

      return ApiResponseBuilder.success(
        responseData,
        'Código reenviado com sucesso'
      );
    } catch (error) {
      throw new HttpException(
        ApiResponseBuilder.error(
          error.message || 'Erro ao reenviar código',
          'RESEND_CODE_ERROR',
          error.details || null
        ),
        error.status || HttpStatus.BAD_REQUEST
      );
    }
  }

  @Post('validate-token')
  @HttpCode(HttpStatus.OK)
  async validateToken(
    @Body() body: { token: string },
  ): Promise<ApiResponse<{ valid: boolean; user?: any; reason?: string }>> {
    try {
      const decoded = await this.jwtService.verifyAsync(body.token);

      const user = await this.prisma.user.findUnique({
        where: { id: decoded.sub },
      });

      if (user) {
        return ApiResponseBuilder.success(
          {
            valid: true,
            user: {
              id: user.id,
              name: user.name,
              phone: user.phone || undefined,
              createdAt: user.createdAt.toISOString(),
            },
          },
          'Token válido',
        );
      } else {
        return ApiResponseBuilder.success(
          { valid: false, reason: 'USER_NOT_FOUND' },
          'Usuário do token não encontrado',
        );
      }
    } catch (error) {
      if (error instanceof TokenExpiredError) {
        return ApiResponseBuilder.success(
          { valid: false, reason: 'TOKEN_EXPIRED' },
          'Token expirado',
        );
      }
      return ApiResponseBuilder.success(
        { valid: false, reason: 'TOKEN_INVALID' },
        'Token inválido',
      );
    }
  }
}
