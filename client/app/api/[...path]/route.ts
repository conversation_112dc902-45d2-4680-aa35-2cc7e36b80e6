import { NextRequest, NextResponse } from 'next/server'

/**
 * Dynamic API route handler that proxies all requests to the backend
 * This eliminates the need for individual route files
 *
 * Note: Backend has global prefix 'api', and v2 controllers use 'api/v1' path
 * Frontend /api/v1/auth/login -> Backend /api/v1/auth/login -> Final /api/api/v1/auth/login
 */

const BACKEND_URL = process.env.BACKEND_URL || 'http://localhost:3000'

async function handleRequest(
  request: NextRequest,
  { params }: { params: Promise<{ path: string[] }> }
) {
  try {
    const resolvedParams = await params
    const path = resolvedParams.path.join('/').replace('v1/', '')
    const backendUrl = `${BACKEND_URL}/api/${path}`
    
    // Get search params from the original request
    const searchParams = request.nextUrl.searchParams.toString()
    const fullUrl = searchParams ? `${backendUrl}?${searchParams}` : backendUrl

    // Forward headers (especially Authorization)
    const headers: Record<string, string> = {}
    
    // Copy important headers
    const headersToForward = [
      'authorization',
      'content-type',
      'user-agent',
      'x-forwarded-for',
      'x-real-ip',
    ]
    
    headersToForward.forEach(headerName => {
      const value = request.headers.get(headerName)
      if (value) {
        headers[headerName] = value
      }
    })

    // Add client IP for security logging
    const clientIp = request.headers.get('x-forwarded-for') || 
                     request.headers.get('x-real-ip') || 
                     'unknown'
    headers['x-client-ip'] = clientIp

    // Prepare fetch options
    const fetchOptions: RequestInit = {
      method: request.method,
      headers,
      // @ts-ignore
      duplex: 'half',
    }

    // Add body for non-GET requests
    if (request.method !== 'GET' && request.method !== 'HEAD') {
      fetchOptions.body = request.body
    }

    // Make request to backend
    const response = await fetch(fullUrl, fetchOptions)
    
    // Get response data
    const responseData = await response.text()
    
    // Create response with same status and headers
    const nextResponse = new NextResponse(responseData, {
      status: response.status,
      statusText: response.statusText,
    })

    // Forward important response headers
    const responseHeadersToForward = [
      'content-type',
      'cache-control',
      'set-cookie',
    ]
    
    responseHeadersToForward.forEach(headerName => {
      const value = response.headers.get(headerName)
      if (value) {
        nextResponse.headers.set(headerName, value)
      }
    })

    // Add CORS headers for development
    if (process.env.NODE_ENV === 'development') {
      nextResponse.headers.set('Access-Control-Allow-Origin', '*')
      nextResponse.headers.set('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
      nextResponse.headers.set('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    }

    return nextResponse

  } catch (error) {
    console.error('API Proxy Error:', error)
    
    return NextResponse.json(
      {
        success: false,
        message: 'Erro interno do servidor',
        error: process.env.NODE_ENV === 'development' ? (error as Error).message : undefined,
      },
      { status: 500 }
    )
  }
}

// Export handlers for all HTTP methods
export async function GET(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function POST(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function PUT(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function DELETE(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function PATCH(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  return handleRequest(request, context)
}

export async function OPTIONS(request: NextRequest, context: { params: Promise<{ path: string[] }> }) {
  // Handle CORS preflight requests
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS, PATCH',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  })
}
